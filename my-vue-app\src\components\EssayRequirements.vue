<template>
  <div class="essay-requirements">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-bars">
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
        </div>
      </div>
      <div class="status-right">
        <div class="battery-indicator">
          <div class="battery-level"></div>
        </div>
      </div>
    </div>

    <!-- 头部导航 -->
    <div class="header">
      <div class="back-button" @click="goBack">
        <img :src="arrowLeft" alt="返回" />
      </div>
      <div class="header-title">作文要求</div>
    </div>

    <!-- 作文信息标签 -->
    <div class="essay-info-bar">
      <span class="info-tag">第三单元</span>
      <span class="info-tag">单元作文</span>
      <span class="info-tag">全命题</span>
    </div>

    <!-- 作文详细信息 -->
    <div class="essay-details">
      <div class="detail-row">
        <span class="detail-label">作文命题</span>
        <span class="detail-value">我的植物朋友</span>
      </div>
      <div class="detail-row">
        <span class="detail-label">字数要求</span>
        <span class="detail-value">300字</span>
      </div>
      <div class="detail-row">
        <span class="detail-label">总分</span>
        <span class="detail-value">30分</span>
      </div>
      <div class="detail-row requirement-row">
        <span class="detail-label">作文要求</span>
        <div class="requirement-text">
          选择一项自己做过的小实验（可以是科学课上的，也可以是自己在家尝试的），按照"实验准备—实验过程—实验结果"的顺序写下来。重点把实验过程写清楚，可以用上"先……接着……然后……最后……"等表示顺序的词语。
        </div>
      </div>
    </div>

    <!-- 分隔线 -->
    <div class="separator"></div>

    <!-- 上传作文区域 -->
    <div class="upload-section">
      <div class="upload-title">
        <span>上传作文</span>
        <img :src="warningCircle" alt="提示" class="warning-icon" />
      </div>

      <!-- 图片上传网格 -->
      <div class="upload-grid">
        <div class="upload-item" v-for="(item, index) in uploadItems" :key="index">
          <div class="upload-box" v-if="!item.hasImage">
            <img :src="item.icon" alt="上传" />
          </div>
          <div class="image-container" v-else>
            <img :src="item.image" alt="已上传" class="uploaded-image" />
            <div class="remove-btn" @click="removeImage(index)">
              <img :src="getMinusIcon(index)" alt="删除" />
            </div>
          </div>
        </div>
        
        <!-- 添加更多照片按钮 -->
        <div class="add-photo-btn" @click="addPhoto">
          <img :src="addPhoto" alt="添加照片" />
        </div>
      </div>
    </div>

    <!-- 提交按钮 -->
    <div class="submit-section">
      <button class="submit-btn" @click="submitEssay">提交</button>
    </div>
  </div>
</template>

<script>
import arrowLeft from '../assets/images/arrow-left.svg'
import warningCircle from '../assets/images/warning-circle.svg'
import uploadImage1 from '../assets/images/upload-image-1.svg'
import uploadImage2 from '../assets/images/upload-image-2.svg'
import uploadImage3 from '../assets/images/upload-image-3.svg'
import uploadImage4 from '../assets/images/upload-image-4.svg'
import addPhoto from '../assets/images/add-photo.svg'
import minusCircle1 from '../assets/images/minus-circle-1.svg'
import minusCircle2 from '../assets/images/minus-circle-2.svg'
import minusCircle3 from '../assets/images/minus-circle-3.svg'
import minusCircle4 from '../assets/images/minus-circle-4.svg'

export default {
  name: 'EssayRequirements',
  emits: ['go-back'],
  data() {
    return {
      arrowLeft,
      warningCircle,
      addPhoto,
      uploadItems: [
        { icon: uploadImage1, hasImage: false, image: null },
        { icon: uploadImage2, hasImage: false, image: null },
        { icon: uploadImage3, hasImage: false, image: null },
        { icon: uploadImage4, hasImage: false, image: null }
      ],
      minusIcons: [minusCircle1, minusCircle2, minusCircle3, minusCircle4]
    }
  },
  methods: {
    goBack() {
      this.$emit('go-back')
    },
    removeImage(index) {
      this.uploadItems[index].hasImage = false
      this.uploadItems[index].image = null
    },
    addPhoto() {
      console.log('添加照片')
      // 这里可以添加文件选择逻辑
    },
    getMinusIcon(index) {
      return this.minusIcons[index] || this.minusIcons[0]
    },
    submitEssay() {
      console.log('提交作文')
      // 这里添加提交逻辑
    }
  }
}
</script>

<style scoped>
.essay-requirements {
  width: 375px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
}

/* 状态栏样式 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.status-left {
  display: flex;
  align-items: center;
}

.signal-bars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.bar {
  width: 2.55px;
  background: #000000;
}

.bar:nth-child(1) { height: 3.4px; }
.bar:nth-child(2) { height: 5.53px; }
.bar:nth-child(3) { height: 8.51px; }
.bar:nth-child(4) { height: 10.21px; }

.status-right {
  display: flex;
  align-items: center;
}

.battery-indicator {
  width: 20.28px;
  height: 10.06px;
  border: 1px solid #000000;
  border-radius: 2px;
  position: relative;
  opacity: 0.35;
}

.battery-level {
  width: 17.87px;
  height: 7.66px;
  background: #000000;
  position: absolute;
  top: 1.2px;
  left: 1.21px;
}

/* 头部导航 */
.header {
  height: 32px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  position: relative;
}

.back-button {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.back-button img {
  width: 100%;
  height: 100%;
}

.header-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 16px;
  line-height: 26px;
  color: #323842;
  font-weight: 400;
}

/* 作文信息标签栏 */
.essay-info-bar {
  height: 34px;
  background: #F8F9FA;
  border: 1px solid #BCC1CA;
  display: flex;
  align-items: center;
  padding: 0 26px;
  gap: 12px;
}

.info-tag {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

/* 作文详细信息 */
.essay-details {
  padding: 18px 26px;
  background: #FFFFFF;
}

.detail-row {
  display: flex;
  margin-bottom: 26px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  width: 44px;
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
  margin-right: 12px;
  flex-shrink: 0;
}

.detail-value {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.requirement-row {
  align-items: flex-start;
}

.requirement-text {
  flex: 1;
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
  max-width: 263px;
}

/* 分隔线 */
.separator {
  height: 20px;
  background: #F8F9FA;
  border: 1px solid #BCC1CA;
}

/* 上传作文区域 */
.upload-section {
  padding: 32px 24px 0;
  background: #FFFFFF;
}

.upload-title {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 13px;
}

.upload-title span {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.warning-icon {
  width: 16px;
  height: 16px;
}

/* 上传网格 */
.upload-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 21px 20px;
  margin-bottom: 32px;
}

.upload-item {
  position: relative;
}

.upload-box {
  width: 131px;
  height: 76px;
  border: 3px solid #BCC1CA;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.upload-box img {
  width: 128px;
  height: 73px;
}

.image-container {
  position: relative;
  width: 131px;
  height: 76px;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.remove-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.remove-btn img {
  width: 100%;
  height: 100%;
}

.add-photo-btn {
  width: 131px;
  height: 76px;
  border: 1px dashed #DEE1E6;
  border-radius: 4px;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.add-photo-btn img {
  width: 131px;
  height: 76px;
}

/* 提交按钮区域 */
.submit-section {
  padding: 32px 12px 52px;
  background: #FFFFFF;
}

.submit-btn {
  width: 350px;
  height: 52px;
  background: #636AE8;
  border: 1px solid rgba(0, 0, 0, 0);
  border-radius: 16px;
  font-size: 18px;
  line-height: 28px;
  color: #FFFFFF;
  font-weight: 400;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn:hover {
  background: #5258d6;
}
</style>
